

/**
* Template Name: MyResume
* Template URL: https://bootstrapmade.com/free-html-bootstrap-template-my-resume/
* Updated: Jun 29 2024 with Bootstrap v5.3.3
* Author: BootstrapMade.com
* License: https://bootstrapmade.com/license/
*/

(function() {
  "use strict";

  /**
   * Header toggle
   */
  const headerToggleBtn = document.querySelector('.header-toggle');

  function headerToggle() {
    document.querySelector('#header').classList.toggle('header-show');
    headerToggleBtn.classList.toggle('bi-list');
    headerToggleBtn.classList.toggle('bi-x');
  }
  headerToggleBtn.addEventListener('click', headerToggle);

  /**
   * Hide mobile nav on same-page/hash links
   */
  document.querySelectorAll('#navmenu a').forEach(navmenu => {
    navmenu.addEventListener('click', () => {
      if (document.querySelector('.header-show')) {
        headerToggle();
      }
    });

  });

  /**
   * Toggle mobile nav dropdowns
   */
  document.querySelectorAll('.navmenu .toggle-dropdown').forEach(navmenu => {
    navmenu.addEventListener('click', function(e) {
      e.preventDefault();
      this.parentNode.classList.toggle('active');
      this.parentNode.nextElementSibling.classList.toggle('dropdown-active');
      e.stopImmediatePropagation();
    });
  });

  /**
   * Preloader
   */
  const preloader = document.querySelector('#preloader');
  if (preloader) {
    window.addEventListener('load', () => {
      preloader.remove();
    });
  }

  /**
   * Scroll top button
   */
  let scrollTop = document.querySelector('.scroll-top');

  function toggleScrollTop() {
    if (scrollTop) {
      window.scrollY > 100 ? scrollTop.classList.add('active') : scrollTop.classList.remove('active');
    }
  }
  scrollTop.addEventListener('click', (e) => {
    e.preventDefault();
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  });

  window.addEventListener('load', toggleScrollTop);
  document.addEventListener('scroll', toggleScrollTop);

  /**
   * Animation on scroll function and init
   */
  function aosInit() {
    AOS.init({
      duration: 600,
      easing: 'ease-in-out',
      once: true,
      mirror: false
    });
  }
  window.addEventListener('load', aosInit);

  /**
   * Init typed.js
   */
  const selectTyped = document.querySelector('.typed');
  if (selectTyped) {
    let typed_strings = selectTyped.getAttribute('data-typed-items');
    typed_strings = typed_strings.split(',');
    new Typed('.typed', {
      strings: typed_strings,
      loop: true,
      typeSpeed: 100,
      backSpeed: 50,
      backDelay: 2000
    });
  }

  /**
   * Initiate Pure Counter
   */
  new PureCounter();

  /**
   * Animate the skills items on reveal
   */
  let skillsAnimation = document.querySelectorAll('.skills-animation');
  skillsAnimation.forEach((item) => {
    new Waypoint({
      element: item,
      offset: '80%',
      handler: function(direction) {
        let progress = item.querySelectorAll('.progress .progress-bar');
        progress.forEach(el => {
          el.style.width = el.getAttribute('aria-valuenow') + '%';
        });
      }
    });
  });

  /**
   * Initiate glightbox
   */
  const glightbox = GLightbox({
    selector: '.glightbox'
  });

  /**
   * Init isotope layout and filters
   */
  document.querySelectorAll('.isotope-layout').forEach(function(isotopeItem) {
    let layout = isotopeItem.getAttribute('data-layout') ?? 'masonry';
    let filter = isotopeItem.getAttribute('data-default-filter') ?? '*';
    let sort = isotopeItem.getAttribute('data-sort') ?? 'original-order';

    let initIsotope;
    imagesLoaded(isotopeItem.querySelector('.isotope-container'), function() {
      initIsotope = new Isotope(isotopeItem.querySelector('.isotope-container'), {
        itemSelector: '.isotope-item',
        layoutMode: layout,
        filter: filter,
        sortBy: sort
      });
    });

    isotopeItem.querySelectorAll('.isotope-filters li').forEach(function(filters) {
      filters.addEventListener('click', function() {
        isotopeItem.querySelector('.isotope-filters .filter-active').classList.remove('filter-active');
        this.classList.add('filter-active');
        initIsotope.arrange({
          filter: this.getAttribute('data-filter')
        });
        if (typeof aosInit === 'function') {
          aosInit();
        }
      }, false);
    });

  });

  /**
   * Init swiper sliders
   */
  function initSwiper() {
    document.querySelectorAll(".init-swiper").forEach(function(swiperElement) {
      let config = JSON.parse(
        swiperElement.querySelector(".swiper-config").innerHTML.trim()
      );

      if (swiperElement.classList.contains("swiper-tab")) {
        initSwiperWithCustomPagination(swiperElement, config);
      } else {
        new Swiper(swiperElement, config);
      }
    });
  }

  window.addEventListener("load", initSwiper);

  /**
   * Correct scrolling position upon page load for URLs containing hash links.
   */
  window.addEventListener('load', function(e) {
    if (window.location.hash) {
      if (document.querySelector(window.location.hash)) {
        setTimeout(() => {
          let section = document.querySelector(window.location.hash);
          let scrollMarginTop = getComputedStyle(section).scrollMarginTop;
          window.scrollTo({
            top: section.offsetTop - parseInt(scrollMarginTop),
            behavior: 'smooth'
          });
        }, 100);
      }
    }
  });

  /**
   * Navmenu Scrollspy
   */
  let navmenulinks = document.querySelectorAll('.navmenu a');

  function navmenuScrollspy() {
    navmenulinks.forEach(navmenulink => {
      if (!navmenulink.hash) return;
      let section = document.querySelector(navmenulink.hash);
      if (!section) return;
      let position = window.scrollY + 200;
      if (position >= section.offsetTop && position <= (section.offsetTop + section.offsetHeight)) {
        document.querySelectorAll('.navmenu a.active').forEach(link => link.classList.remove('active'));
        navmenulink.classList.add('active');
      } else {
        navmenulink.classList.remove('active');
      }
    })
  }
  window.addEventListener('load', navmenuScrollspy);
  document.addEventListener('scroll', navmenuScrollspy);

  /*--------------------------------------------------------------
  # Custom Features - Language Toggle, Tabs, Modals, and Hobbies
  --------------------------------------------------------------*/

  /**
   * Language Toggle Functionality
   */
  const langButtons = document.querySelectorAll('.lang-btn');
  const langContents = document.querySelectorAll('.lang-content');

  function updateLanguage(selectedLang) {
    if (!selectedLang) return;

    // Update content visibility
    langContents.forEach(content => {
      content.classList.remove('active');
      if (content.classList.contains(selectedLang)) {
        content.classList.add('active');
      }
    });

    // Update modal links visibility
    const modalLinks = document.querySelectorAll('.modal-link');
    modalLinks.forEach(link => {
      link.classList.remove('active');
      if (link.classList.contains(selectedLang)) {
        link.classList.add('active');
      }
    });

    // Update hobby links visibility
    const hobbyLinks = document.querySelectorAll('.hobby-link');
    hobbyLinks.forEach(link => {
      link.classList.remove('active');
      if (link.classList.contains(selectedLang)) {
        link.classList.add('active');
      }
    });

    // Update tab button tooltips
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => {
      const tooltipAttr = selectedLang === 'en' ? 'data-tooltip-en' : 'data-tooltip-ms';
      const tooltipText = btn.getAttribute(tooltipAttr);
      if (tooltipText) {
        btn.setAttribute('data-tooltip-current', tooltipText);
      }
    });
  }

  // Initialize language on page load
  document.addEventListener('DOMContentLoaded', () => {
    updateLanguage('en');
  });

  // Language button event listeners
  langButtons.forEach(button => {
    button.addEventListener('click', () => {
      const selectedLang = button.getAttribute('data-lang');
      if (!selectedLang) return;

      // Update active button
      langButtons.forEach(btn => btn.classList.remove('active'));
      button.classList.add('active');

      // Update language
      updateLanguage(selectedLang);
    });
  });

  /**
   * Tab Switching Functionality
   */
  const tabButtons = document.querySelectorAll('.tab-btn');
  const tabContents = document.querySelectorAll('.tab-content');

  // Initialize - hide all content on page load
  tabContents.forEach(content => content.classList.remove('active'));

  tabButtons.forEach(button => {
    button.addEventListener('click', () => {
      const tabName = button.getAttribute('data-tab');
      if (!tabName) return;

      // Remove active class from all buttons and contents
      tabButtons.forEach(btn => {
        btn.classList.remove('active');
        if (!btn.classList.contains('tab-separator')) {
          btn.classList.add('inactive');
        }
      });
      tabContents.forEach(content => content.classList.remove('active'));

      // Add active class to clicked button and corresponding content
      button.classList.add('active');
      button.classList.remove('inactive');

      const targetContent = document.getElementById(tabName);
      if (targetContent) {
        targetContent.classList.add('active');
      }
    });
  });

  /**
   * Project Modal Functionality
   */
  const projectLogos = document.querySelectorAll('.project-logo');
  const modals = document.querySelectorAll('.modal');
  const modalCloseButtons = document.querySelectorAll('.modal-close');

  // Project logo click handlers
  projectLogos.forEach(logo => {
    logo.addEventListener('click', () => {
      const projectNum = logo.getAttribute('data-project');
      if (!projectNum) return;

      const modal = document.getElementById(`projectModal${projectNum}`);
      if (modal) {
        modal.classList.add('active');
        // Prevent body scroll when modal is open
        document.body.style.overflow = 'hidden';
      }
    });
  });

  // Modal close button handlers
  modalCloseButtons.forEach(btn => {
    btn.addEventListener('click', (e) => {
      e.stopPropagation();
      const modal = btn.closest('.modal');
      if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
      }
    });
  });

  // Click outside modal to close
  modals.forEach(modal => {
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
      }
    });
  });

  /**
   * PDF Modal Functionality
   */
  const certPdfButtons = document.querySelectorAll('.cert-pdf-btn');
  const pdfModal = document.getElementById('pdfModal');
  const pdfViewer = document.getElementById('pdfViewer');
  const pdfModalClose = document.querySelector('.pdf-modal-close');

  // PDF button click handlers
  certPdfButtons.forEach(btn => {
    btn.addEventListener('click', () => {
      const pdfFile = btn.getAttribute('data-pdf');
      if (!pdfFile || !pdfModal || !pdfViewer) return;

      pdfViewer.src = `pdfs/${pdfFile}`;
      pdfModal.classList.add('active');
      document.body.style.overflow = 'hidden';
    });
  });

  // PDF modal close button
  if (pdfModalClose) {
    pdfModalClose.addEventListener('click', () => {
      if (pdfModal && pdfViewer) {
        pdfModal.classList.remove('active');
        pdfViewer.src = '';
        document.body.style.overflow = '';
      }
    });
  }

  // Click outside PDF modal to close
  if (pdfModal) {
    pdfModal.addEventListener('click', (e) => {
      if (e.target === pdfModal && pdfViewer) {
        pdfModal.classList.remove('active');
        pdfViewer.src = '';
        document.body.style.overflow = '';
      }
    });
  }

  /**
   * Hobby Image Shuffle Functionality
   */
  const hobbyImages = document.querySelectorAll('.hobby-image');
  const imageArray = Array.from(hobbyImages);

  function shuffleImages() {
    if (imageArray.length === 0) return;

    // Fade out all images
    imageArray.forEach(img => {
      img.style.opacity = '0';
    });

    // Wait for fade out, then shuffle
    setTimeout(() => {
      // Create array of image sources
      const sources = imageArray.map(img => img.src);

      // Shuffle sources array using Fisher-Yates algorithm
      for (let i = sources.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [sources[i], sources[j]] = [sources[j], sources[i]];
      }

      // Apply shuffled sources to images
      imageArray.forEach((img, index) => {
        if (sources[index]) {
          img.src = sources[index];
        }
      });

      // Fade in all images
      imageArray.forEach(img => {
        img.style.opacity = '1';
      });
    }, 250);
  }

  // Initialize hobby image shuffle
  if (imageArray.length > 0) {
    // Shuffle every 5 seconds
    setInterval(shuffleImages, 5000);
  }

  /**
   * Keyboard Navigation Support
   */
  document.addEventListener('keydown', (e) => {
    // Close modals with Escape key
    if (e.key === 'Escape') {
      // Close project modals
      modals.forEach(modal => {
        if (modal.classList.contains('active')) {
          modal.classList.remove('active');
          document.body.style.overflow = '';
        }
      });

      // Close PDF modal
      if (pdfModal && pdfModal.classList.contains('active')) {
        pdfModal.classList.remove('active');
        if (pdfViewer) pdfViewer.src = '';
        document.body.style.overflow = '';
      }
    }
  });

})();
