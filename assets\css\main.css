/**
* Template Name: MyResume
* Template URL: https://bootstrapmade.com/free-html-bootstrap-template-my-resume/
* Updated: Jun 29 2024 with Bootstrap v5.3.3
* Author: BootstrapMade.com
* License: https://bootstrapmade.com/license/
*/

/*--------------------------------------------------------------
# Font & Color Variables
# Help: https://bootstrapmade.com/color-system/
--------------------------------------------------------------*/
/* Fonts */
:root {
  --default-font: "Roboto",  system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --heading-font: "Raleway",  sans-serif;
  --nav-font: "Poppins",  sans-serif;
}

/* Global Colors - The following color variables are used throughout the website. Updating them here will change the color scheme of the entire website */
:root { 
  --background-color: #ffffff; /* Background color for the entire website, including individual sections */
  --default-color: #272829; /* Default color used for the majority of the text content across the entire website */
  --heading-color: #45505b; /* Color for headings, subheadings and title throughout the website */
  --accent-color: #0563bb; /* Accent color that represents your brand on the website. It's used for buttons, links, and other elements that need to stand out */
  --surface-color: #ffffff; /* The surface color is used as a background of boxed elements within sections, such as cards, icon boxes, or other elements that require a visual separation from the global background. */
  --contrast-color: #ffffff; /* Contrast color for text, ensuring readability against backgrounds of accent, heading, or default colors. */
}

/* Nav Menu Colors - The following color variables are used specifically for the navigation menu. They are separate from the global colors to allow for more customization options */
:root {
  --nav-color: #45505b;  /* The default color of the main navmenu links */
  --nav-hover-color: #0563bb; /* Applied to main navmenu links when they are hovered over or active */
  --nav-mobile-background-color: #ffffff; /* Used as the background color for mobile navigation menu */
  --nav-dropdown-background-color: #ffffff; /* Used as the background color for dropdown items that appear when hovering over primary navigation items */
  --nav-dropdown-color: #212529; /* Used for navigation links of the dropdown items in the navigation menu. */
  --nav-dropdown-hover-color: #0563bb; /* Similar to --nav-hover-color, this color is applied to dropdown navigation links when they are hovered over. */
}

/* Color Presets - These classes override global colors when applied to any section or element, providing reuse of the sam color scheme. */

.light-background {
  --background-color: #f9f9f9;
  --surface-color: #ffffff;
}

.dark-background {
  --background-color: #060606;
  --default-color: #ffffff;
  --heading-color: #ffffff;
  --surface-color: #252525;
  --contrast-color: #ffffff;
}

/* Smooth scroll */
:root {
  scroll-behavior: smooth;
}

/*--------------------------------------------------------------
# General Styling & Shared Classes
--------------------------------------------------------------*/
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  color: var(--default-color);
  background-color: var(--background-color);
  font-family: var(--default-font);
  line-height: 1.6;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

a {
  color: var(--accent-color);
  text-decoration: none;
  transition: 0.3s;
}

a:hover {
  color: color-mix(in srgb, var(--accent-color), transparent 25%);
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--heading-color);
  font-family: var(--heading-font);
}

/* Container fixes */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

@media (min-width: 1400px) {
  .container {
    max-width: 1320px;
  }
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #222;
    background: #fff;
}

h1, h2, h3 {
    font-family: 'Poppins', sans-serif;
}

/* PHP Email Form Messages
------------------------------*/
.php-email-form .error-message {
  display: none;
  background: #df1529;
  color: #ffffff;
  text-align: left;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .sent-message {
  display: none;
  color: #ffffff;
  background: #059652;
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
  font-weight: 600;
}

.php-email-form .loading {
  display: none;
  background: var(--surface-color);
  text-align: center;
  padding: 15px;
  margin-bottom: 24px;
}

.php-email-form .loading:before {
  content: "";
  display: inline-block;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin: 0 10px -6px 0;
  border: 3px solid var(--accent-color);
  border-top-color: var(--surface-color);
  animation: php-email-form-loading 1s linear infinite;
}

@keyframes php-email-form-loading {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Global Header
--------------------------------------------------------------*/
.header {
  color: var(--default-color);
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  padding: 0 15px;
  transition: all ease-in-out 0.3s;
  overflow-y: auto;
  z-index: 997;
  width: 200px;
  background-color: var(--background-color);
  border-right: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

/* Mobile and Tablet - Hide sidebar by default */
@media (max-width: 1199px) {
  .header {
    background-color: var(--background-color);
    border-right: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
    width: 280px;
    left: -100%;
    transition: left 0.3s ease;
  }

  .header.header-show {
    left: 0;
  }

  .main {
    margin-left: 0 !important;
    width: 100%;
  }
}

/* Desktop - Full sidebar with transparent background */
@media (min-width: 1200px) {
  .header {
    left: 0;
    top: 0;
    bottom: 0;
    width: 200px;
    background-color: transparent !important;
    backdrop-filter: none;
    box-shadow: none;
    border: none;
    padding: 0 15px;
  }

  /* Keep main content centered - no interference from sidebar */
  .main {
    margin-left: 0 !important;
    width: 100% !important;
    padding-left: 0;
  }

  /* Ensure all sections remain centered */
  .section {
    width: 100%;
    margin: 0 auto;
  }

  .container {
    margin: 0 auto;
    width: 100%;
    max-width: 1200px;
  }
}

/* Ensure hero section spans full width even with sidebar */
@media (min-width: 1200px) {
  .hero {
    margin-left: 0 !important;
    width: 100vw !important;
    position: relative;
    left: 0;
  }
}

.header.header-show {
  left: 0;
}

.header .header-toggle {
  color: var(--contrast-color);
  background-color: var(--accent-color);
  font-size: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  position: fixed;
  top: 15px;
  right: 15px;
  z-index: 9999;
  transition: background-color 0.3s;
}

/* Hide toggle button on desktop since sidebar is always visible */
@media (min-width: 1200px) {
  .header .header-toggle {
    display: none;
  }
}

.header .header-toggle:hover {
  color: var(--contrast-color);
  background-color: color-mix(in srgb, var(--accent-color) 90%, white 15%);
}

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
.navmenu {
  padding: 0;
  z-index: 9997;
}

.navmenu ul {
  list-style: none;
  padding: 0 0 20px 0;
  margin: 0;
  width: 140px;
}

.navmenu a,
.navmenu a:focus {
  color: var(--nav-color);
  font-family: var(--nav-font);
  display: flex;
  align-items: center;
  padding: 10px 18px;
  margin-bottom: 8px;
  font-size: 15px;
  border-radius: 50px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  height: 56px;
  width: 100%;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navmenu a i,
.navmenu a:focus i {
  font-size: 20px;
  min-width: 20px;
  transition: all 0.4s ease;
}

@media (min-width: 992px) {
  .navmenu a i,
  .navmenu a:focus i {
    margin: 0 !important;
    padding: 0 !important;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    text-align: center;
  }
}

.navmenu a span,
.navmenu a:focus span {
  padding: 0 5px 0 7px;
}

@media (min-width: 992px) {
  .navmenu a,
  .navmenu a:focus {
    max-width: 56px !important;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 50px;
    width: 56px !important;
    height: 56px !important;
    justify-content: center !important;
    align-items: center !important;
    transition: all 0.4s ease;
    overflow: hidden;
    padding: 0 !important;
    margin: 0 auto 8px auto;
    display: flex !important;
  }

  .navmenu a span,
  .navmenu a:focus span {
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.4s ease;
    white-space: nowrap;
    padding: 0 5px 0 7px;
    width: 0;
  }

  .navmenu .active,
  .navmenu .active:focus {
    background: var(--accent-color);
    color: var(--contrast-color);
    max-width: 56px;
    width: 56px;
    justify-content: center;
  }

  .navmenu .active span,
  .navmenu .active:focus span {
    opacity: 0;
    transform: translateX(-10px);
    width: 0;
  }

  .navmenu a:hover {
    max-width: 200px;
    width: auto;
    background: var(--accent-color);
    color: var(--contrast-color);
    justify-content: flex-start;
    padding: 10px 20px 10px 18px;
  }

  .navmenu a:hover span {
    opacity: 1;
    transform: translateX(0);
    width: auto;
  }

  .navmenu .active:hover {
    max-width: 200px;
    width: auto;
    justify-content: flex-start;
    padding: 10px 20px 10px 18px;
  }

  .navmenu .active:hover span {
    opacity: 1;
    transform: translateX(0);
    width: auto;
  }
}

/* Mobile navmenu styling */
@media (max-width: 991px) {
  .navmenu a,
  .navmenu a:focus {
    max-width: 100%;
  }

  .navmenu a span,
  .navmenu a:focus span {
    display: block;
    padding: 0 5px 0 7px;
  }

  .navmenu a:hover,
  .navmenu .active,
  .navmenu .active:focus,
  .navmenu li:hover>a {
    color: var(--contrast-color);
    background: var(--nav-hover-color);
  }
}

/* Footer Styles */
#footer {
    background: #f8f9fa;
    padding: 3rem 0 1rem;
    border-top: 1px solid #e9ecef;
    margin-top: 2rem;
}

.footer-column {
    text-align: center;
    margin-bottom: 2rem;
}

.footer-column h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.footer-column p {
    color: #6c757d;
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.footer-column p i {
    color: #007bff;
    font-size: 1rem;
}

.footer-column a {
    color: #6c757d;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-column a:hover {
    color: #007bff;
}

/* Social Icons */
.social-icons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 0.5rem;
}

.social-link {
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #f0f0f0;
    color: #222 !important;
    text-decoration: none;
    transition: all 0.3s;
    font-size: 1.6rem !important;
    margin-bottom: 0 !important;
}

.social-link:hover {
    background: #e0e0e0;
    transform: scale(1.1);
}

/* Tooltip for social links */
.social-link {
    position: relative;
}

.social-link::after {
    content: attr(title);
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #222;
    color: #fff;
    padding: 0.5rem 0.8rem;
    border-radius: 4px;
    font-size: 0.85rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    margin-top: 0.5rem;
}

.social-link:hover::after {
    opacity: 1;
}

.footer-bottom {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #dee2e6;
}

.footer-bottom .copyright {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0;
}

.footer-bottom .credits {
    color: #adb5bd;
    font-size: 0.85rem;
}

.footer-bottom .credits a {
    color: #007bff;
    text-decoration: none;
}

.footer-bottom .credits a:hover {
    text-decoration: underline;
}

/* Layout Fixes - Force Center Layout */
.main {
  width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
  overflow-x: hidden;
  position: relative;
}

.section {
  width: 100%;
  padding: 60px 0;
  margin: 0 auto;
}

/* Ensure all images are responsive */
img {
  max-width: 100%;
  height: auto;
}

/* Center all content containers */
.container {
  margin-left: auto !important;
  margin-right: auto !important;
}

/* Fix for mobile viewport */
@media (max-width: 767px) {
  body {
    font-size: 14px;
  }

  .container {
    padding: 0 10px;
  }

  .section {
    padding: 40px 0;
  }

  /* Ensure main content doesn't have left margin on mobile */
  .main {
    margin-left: 0 !important;
  }

  /* Fix hero section for mobile */
  .hero {
    padding: 40px 0;
    min-height: 70vh;
  }

  .hero h2 {
    font-size: 1.8rem !important;
  }

  .hero p {
    font-size: 1rem;
  }
}

/* Smooth Scroll */
html {
    scroll-behavior: smooth;
}

/*--------------------------------------------------------------
# Preloader
--------------------------------------------------------------*/
#preloader {
  position: fixed;
  inset: 0;
  z-index: 999999;
  overflow: hidden;
  background: var(--background-color);
  transition: all 0.6s ease-out;
}

#preloader:before {
  content: "";
  position: fixed;
  top: calc(50% - 30px);
  left: calc(50% - 30px);
  border: 6px solid #ffffff;
  border-color: var(--accent-color) transparent var(--accent-color) transparent;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  animation: animate-preloader 1.5s linear infinite;
}

@keyframes animate-preloader {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/*--------------------------------------------------------------
# Scroll Top Button
--------------------------------------------------------------*/
.scroll-top {
  position: fixed;
  visibility: hidden;
  opacity: 0;
  right: 15px;
  bottom: -15px;
  z-index: 99999;
  background-color: var(--accent-color);
  width: 44px;
  height: 44px;
  border-radius: 50px;
  transition: all 0.4s;
}

.scroll-top i {
  font-size: 24px;
  color: var(--contrast-color);
  line-height: 0;
}

.scroll-top:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 20%);
  color: var(--contrast-color);
}

.scroll-top.active {
  visibility: visible;
  opacity: 1;
  bottom: 15px;
}

/*--------------------------------------------------------------
# Disable aos animation delay on mobile devices
--------------------------------------------------------------*/
@media screen and (max-width: 768px) {
  [data-aos-delay] {
    transition-delay: 0 !important;
  }
}

/*--------------------------------------------------------------
# Global Page Titles & Breadcrumbs
--------------------------------------------------------------*/
.page-title {
  --background-color: color-mix(in srgb, var(--default-color), transparent 96%);
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 20px 0;
  position: relative;
}

.page-title h1 {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
}

.page-title .breadcrumbs ol {
  display: flex;
  flex-wrap: wrap;
  list-style: none;
  padding: 0 0 10px 0;
  margin: 0;
  font-size: 14px;
  font-weight: 400;
}

.page-title .breadcrumbs ol li+li {
  padding-left: 10px;
}

.page-title .breadcrumbs ol li+li::before {
  content: "/";
  display: inline-block;
  padding-right: 10px;
  color: color-mix(in srgb, var(--default-color), transparent 70%);
}

/*--------------------------------------------------------------
# Global Sections
--------------------------------------------------------------*/
section,
.section {
  color: var(--default-color);
  background-color: var(--background-color);
  padding: 60px 0;
  overflow: clip;
}

/*--------------------------------------------------------------
# Global Section Titles
--------------------------------------------------------------*/
.section-title {
  text-align: center;
  padding-bottom: 60px;
  position: relative;
}

.section-title h2 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
  padding-bottom: 20px;
  position: relative;
}

.section-title h2:before {
  content: "";
  position: absolute;
  display: block;
  width: 160px;
  height: 1px;
  background: color-mix(in srgb, var(--default-color), transparent 60%);
  left: 0;
  right: 0;
  bottom: 1px;
  margin: auto;
}

.section-title h2::after {
  content: "";
  position: absolute;
  display: block;
  width: 60px;
  height: 3px;
  background: var(--accent-color);
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}

.section-title p {
  margin-bottom: 0;
}

/*--------------------------------------------------------------
# Hero Section
--------------------------------------------------------------*/
.hero {
  width: 100% !important;
  min-height: 100vh;
  position: relative;
  padding: 80px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

.hero img {
  position: absolute;
  inset: 0;
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
}

.hero:before {
  content: "";
  background: color-mix(in srgb, var(--background-color), transparent 30%);
  position: absolute;
  inset: 0;
  z-index: 2;
}

.hero .container {
  position: relative;
  z-index: 3;
}

.hero h2 {
  margin: 0;
  font-size: 64px;
  font-weight: 700;
}

.hero p {
  margin: 5px 0 0 0;
  font-size: 26px;
}

.hero p span {
  letter-spacing: 1px;
  border-bottom: 2px solid var(--accent-color);
}

.hero .social-links {
  margin-top: 25px;
}

.hero .social-links a {
  font-size: 20px;
  display: inline-block;
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  line-height: 1;
  margin-right: 20px;
  transition: 0.3s;
}

.hero .social-links a:hover {
  color: var(--accent-color);
}

@media (max-width: 768px) {
  .hero h2 {
    font-size: 32px;
  }

  .hero p {
    font-size: 20px;
  }
}

/*--------------------------------------------------------------
# About Section
--------------------------------------------------------------*/
.about .content h2 {
  font-weight: 700;
  font-size: 24px;
}

.about .content ul {
  list-style: none;
  padding: 0;
}

.about .content ul li {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.about .content ul strong {
  margin-right: 10px;
}

.about .content ul i {
  font-size: 16px;
  margin-right: 5px;
  color: var(--accent-color);
  line-height: 0;
}

/*--------------------------------------------------------------
# Stats Section
--------------------------------------------------------------*/
.stats i {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  width: 54px;
  height: 54px;
  font-size: 24px;
  border-radius: 50px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.stats .stats-item {
  margin-top: 10px;
  width: 100%;
  position: relative;
  text-align: center;
  z-index: 0;
}

.stats .stats-item span {
  font-size: 36px;
  display: block;
  font-weight: 700;
  color: color-mix(in srgb, var(--default-color), transparent 20%);
}

.stats .stats-item p {
  padding: 0;
  margin: 0;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  font-size: 16px;
}

/*--------------------------------------------------------------
# Skills Section
--------------------------------------------------------------*/
.skills-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.skill-item {
    background: #f9f9f9;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
    color: #222;
    transition: all 0.3s;
}

.skill-item:hover {
    background: #e5e5e5;
    transform: scale(1.05);
}

/*--------------------------------------------------------------
# Resume Section
--------------------------------------------------------------*/

/* Resume Section Tabs */
.resume-tabs {
    display: flex;
    gap: 2rem;
    margin-bottom: 3rem;
    margin-top: 35px;
    font-size: 1.1rem;
    justify-content: center;
}

.tab-btn {
    background: none;
    border: none;
    font-size: 1.1rem;
    font-weight: 500;
    color: #222;
    cursor: pointer;
    padding: 0.5rem 0;
    transition: all 0.3s;
    position: relative;
}

.tab-btn.inactive {
    color: #ccc;
}

.tab-btn:hover {
    color: #555;
}

/* Tab button tooltip */
.tab-btn::after {
    content: attr(data-tooltip-current);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 0.5rem 0.75rem;
    border-radius: 4px;
    font-size: 0.85rem;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    margin-bottom: 0.5rem;
    font-weight: 400;
}

.tab-btn:hover::after {
    opacity: 1;
}

.tab-separator {
    color: #ccc;
}

/* Tab Content */
.tab-content {
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transition: max-height 0.5s ease-in-out, opacity 0.5s ease-in-out;
}

.tab-content.active {
    max-height: 2000px;
    opacity: 1;
}

/* Education Timeline */
.timeline {
    position: relative;
    padding: 2rem 0;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 100%;
    background: #e5e5e5;
}

.timeline-item {
    margin-bottom: 3rem;
    position: relative;
}

.timeline-item:nth-child(odd) .timeline-content {
    margin-left: 0;
    margin-right: auto;
    width: 45%;
    text-align: right;
}

.timeline-item:nth-child(even) .timeline-content {
    margin-left: auto;
    margin-right: 0;
    width: 45%;
    text-align: left;
}

.timeline-dot {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
    width: 12px;
    height: 12px;
    background: #222;
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 0 0 2px #e5e5e5;
}

.timeline-content {
    background: #f9f9f9;
    padding: 1.5rem;
    border-radius: 8px;
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
}

.timeline-content.left-logo {
    flex-direction: row;
}

.timeline-content.right-logo {
    flex-direction: row-reverse;
}

.timeline-logo {
    width: 120px;
    height: 120px;
    min-width: 120px;
    border-radius: 8px;
    object-fit: cover;
}

.timeline-text {
    flex: 1;
}

.timeline-content h3 {
    font-size: 1rem;
    margin-bottom: 0.3rem;
    color: #222;
    margin-top: 0;
}

.education-degree {
    font-size: 0.95rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.timeline-content .year {
    font-size: 0.9rem;
    color: #999;
    margin-bottom: 0.8rem;
}

.timeline-content ul {
    list-style: none;
    font-size: 0.95rem;
    color: #555;
}

.timeline-content li {
    margin-bottom: 0.4rem;
}

.timeline-content li::before {
    content: '• ';
    color: #999;
    margin-right: 0.5rem;
}

/* Work Experience Grid */
.work-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
}

.work-card {
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
    background: #f9f9f9;
    padding: 2rem;
    border-radius: 8px;
    transition: all 0.3s;
}

.work-logo {
    width: 150px;
    height: 150px;
    object-fit: contain;
    flex-shrink: 0;
    min-width: 150px;
}

.work-text {
    flex: 1;
}

.work-card h3 {
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
    color: #222;
    margin-top: 0;
}

.work-card .position {
    font-size: 0.95rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.work-card .year {
    font-size: 0.85rem;
    color: #999;
    margin-bottom: 1rem;
}

.work-card ul {
    list-style: none;
    font-size: 0.9rem;
    color: #555;
}

.work-card li {
    margin-bottom: 0.5rem;
}

.work-card li::before {
    content: '• ';
    color: #999;
    margin-right: 0.5rem;
}

/* Skills List */
.skills-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.skill-item {
    background: #f9f9f9;
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    font-weight: 500;
    color: #222;
    transition: all 0.3s;
}

.skill-item:hover {
    background: #e5e5e5;
    transform: scale(1.05);
}

/* Certifications List */
.certifications-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.certification-item {
    display: flex;
    gap: 2rem;
    align-items: flex-start;
    padding: 1.5rem;
    background: #fff;
    border-radius: 0;
    border-bottom: 1px solid #e5e5e5;
}

.certification-item:last-child {
    border-bottom: none;
}

.cert-logo {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
    flex-shrink: 0;
}

.cert-content {
    flex: 1;
}

.cert-content h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: #222;
}

.cert-issuer {
    font-size: 0.95rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.cert-date {
    font-size: 0.9rem;
    color: #999;
    margin-bottom: 1rem;
}

.cert-pdf-btn {
    display: inline-block;
    color: #222;
    text-decoration: underline;
    cursor: pointer;
    transition: color 0.3s;
    font-weight: 500;
    font-style: italic;
    background: none;
    border: none;
    padding: 0;
    font-size: 0.95rem;
}

.cert-pdf-btn:hover {
    color: #666;
}

/* PDF Modal */
.pdf-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s;
}

.pdf-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.pdf-modal-content {
    background-color: white;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 900px;
    height: 80vh;
    position: relative;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.pdf-modal-close {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 28px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
    background: none;
    border: none;
    z-index: 2001;
}

.pdf-modal-close:hover {
    color: #000;
}

.pdf-viewer {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
}

.pdf-viewer iframe {
    width: 100%;
    height: 100%;
    border: none;
}
/* Project Section */
.project-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    justify-items: center;
    margin-top: 35px;
}

.tooltip a {
    display: inline-block;
    text-decoration: none;
}

.project-logo {
    width: 150px;
    height: 150px;
    object-fit: contain;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    display: block;
}

.project-logo:hover {
    transform: scale(1.05);
}

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    background-color: #f0f0f0;
    color: #333;
    text-align: center;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    position: absolute;
    z-index: 1;
    top: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.8rem;
    white-space: nowrap;
    border: 1px solid #ddd;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}
/*--------------------------------------------------------------
# Portfolio Section
--------------------------------------------------------------*/
.portfolio .portfolio-filters {
  padding: 0;
  margin: 0 auto 20px auto;
  list-style: none;
  text-align: center;
}

.portfolio .portfolio-filters li {
  cursor: pointer;
  display: inline-block;
  padding: 8px 20px 10px 20px;
  margin: 0;
  font-size: 15px;
  font-weight: 500;
  line-height: 1;
  margin-bottom: 5px;
  border-radius: 50px;
  transition: all 0.3s ease-in-out;
  font-family: var(--heading-font);
}

.portfolio .portfolio-filters li:hover,
.portfolio .portfolio-filters li.filter-active {
  color: var(--contrast-color);
  background-color: var(--accent-color);
}

.portfolio .portfolio-filters li:first-child {
  margin-left: 0;
}

.portfolio .portfolio-filters li:last-child {
  margin-right: 0;
}

@media (max-width: 575px) {
  .portfolio .portfolio-filters li {
    font-size: 14px;
    margin: 0 0 10px 0;
  }
}

.portfolio .portfolio-item {
  position: relative;
  overflow: hidden;
}

.portfolio .portfolio-item .portfolio-info {
  opacity: 0;
  position: absolute;
  left: 12px;
  right: 12px;
  bottom: -100%;
  z-index: 3;
  transition: all ease-in-out 0.5s;
  background: color-mix(in srgb, var(--background-color), transparent 10%);
  padding: 15px;
}

.portfolio .portfolio-item .portfolio-info h4 {
  font-size: 18px;
  font-weight: 600;
  padding-right: 50px;
}

.portfolio .portfolio-item .portfolio-info p {
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  font-size: 14px;
  margin-bottom: 0;
  padding-right: 50px;
}

.portfolio .portfolio-item .portfolio-info .preview-link,
.portfolio .portfolio-item .portfolio-info .details-link {
  position: absolute;
  right: 50px;
  font-size: 24px;
  top: calc(50% - 14px);
  color: color-mix(in srgb, var(--default-color), transparent 30%);
  transition: 0.3s;
  line-height: 0;
}

.portfolio .portfolio-item .portfolio-info .preview-link:hover,
.portfolio .portfolio-item .portfolio-info .details-link:hover {
  color: var(--accent-color);
}

.portfolio .portfolio-item .portfolio-info .details-link {
  right: 14px;
  font-size: 28px;
}

.portfolio .portfolio-item:hover .portfolio-info {
  opacity: 1;
  bottom: 0;
}

/*--------------------------------------------------------------
# Services Section
--------------------------------------------------------------*/
.services .service-item {
  background-color: var(--surface-color);
  box-shadow: 0px 5px 90px 0px rgba(0, 0, 0, 0.1);
  height: 100%;
  padding: 60px 30px;
  text-align: center;
  transition: 0.3s;
  border-radius: 5px;
}

.services .service-item .icon {
  margin: 0 auto;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: ease-in-out 0.3s;
  position: relative;
}

.services .service-item .icon i {
  font-size: 36px;
  transition: 0.5s;
  position: relative;
}

.services .service-item .icon svg {
  position: absolute;
  top: 0;
  left: 0;
}

.services .service-item .icon svg path {
  transition: 0.5s;
  fill: color-mix(in srgb, var(--default-color), transparent 95%);
}

.services .service-item h3 {
  font-weight: 700;
  margin: 10px 0 15px 0;
  font-size: 22px;
}

.services .service-item p {
  line-height: 24px;
  font-size: 14px;
  margin-bottom: 0;
}

.services .service-item:hover {
  box-shadow: 0px 5px 35px 0px rgba(0, 0, 0, 0.1);
}

.services .service-item.item-cyan i {
  color: #0dcaf0;
}

.services .service-item.item-cyan:hover .icon i {
  color: #fff;
}

.services .service-item.item-cyan:hover .icon path {
  fill: #0dcaf0;
}

.services .service-item.item-orange i {
  color: #fd7e14;
}

.services .service-item.item-orange:hover .icon i {
  color: #fff;
}

.services .service-item.item-orange:hover .icon path {
  fill: #fd7e14;
}

.services .service-item.item-teal i {
  color: #20c997;
}

.services .service-item.item-teal:hover .icon i {
  color: #fff;
}

.services .service-item.item-teal:hover .icon path {
  fill: #20c997;
}

.services .service-item.item-red i {
  color: #df1529;
}

.services .service-item.item-red:hover .icon i {
  color: #fff;
}

.services .service-item.item-red:hover .icon path {
  fill: #df1529;
}

.services .service-item.item-indigo i {
  color: #6610f2;
}

.services .service-item.item-indigo:hover .icon i {
  color: #fff;
}

.services .service-item.item-indigo:hover .icon path {
  fill: #6610f2;
}

.services .service-item.item-pink i {
  color: #f3268c;
}

.services .service-item.item-pink:hover .icon i {
  color: #fff;
}

.services .service-item.item-pink:hover .icon path {
  fill: #f3268c;
}

/*--------------------------------------------------------------
# Testimonials Section
--------------------------------------------------------------*/
.testimonials .testimonials-carousel,
.testimonials .testimonials-slider {
  overflow: hidden;
}

.testimonials .testimonial-item .testimonial-content {
  border-left: 3px solid var(--accent-color);
  padding-left: 30px;
}

.testimonials .testimonial-item .testimonial-img {
  border-radius: 50%;
  border: 4px solid var(--background-color);
  margin: 0 auto;
}

.testimonials .testimonial-item h3 {
  font-size: 20px;
  font-weight: bold;
  margin: 10px 0 5px 0;
}

.testimonials .testimonial-item h4 {
  font-size: 14px;
  color: color-mix(in srgb, var(--default-color), transparent 40%);
  margin: 0 0 10px 0;
}

.testimonials .testimonial-item .stars i {
  color: #ffc107;
  margin: 0 1px;
}

.testimonials .testimonial-item .quote-icon-left,
.testimonials .testimonial-item .quote-icon-right {
  color: color-mix(in srgb, var(--accent-color), transparent 50%);
  font-size: 26px;
  line-height: 0;
}

.testimonials .testimonial-item .quote-icon-left {
  display: inline-block;
  left: -5px;
  position: relative;
}

.testimonials .testimonial-item .quote-icon-right {
  display: inline-block;
  right: -5px;
  position: relative;
  top: 10px;
  transform: scale(-1, -1);
}

.testimonials .testimonial-item p {
  font-style: italic;
}

.testimonials .swiper-wrapper {
  height: auto;
}

.testimonials .swiper-pagination {
  margin-top: 30px;
  position: relative;
}

.testimonials .swiper-pagination .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background-color: color-mix(in srgb, var(--default-color), transparent 85%);
  opacity: 1;
}

.testimonials .swiper-pagination .swiper-pagination-bullet-active {
  background-color: var(--accent-color);
}

/*--------------------------------------------------------------
# Contact Section
--------------------------------------------------------------*/
.contact .info-item+.info-item {
  margin-top: 40px;
}

.contact .info-item i {
  color: var(--accent-color);
  background: color-mix(in srgb, var(--accent-color), transparent 92%);
  font-size: 20px;
  width: 44px;
  height: 44px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50px;
  transition: all 0.3s ease-in-out;
  margin-right: 15px;
}

.contact .info-item h3 {
  padding: 0;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 5px;
}

.contact .info-item p {
  padding: 0;
  margin-bottom: 0;
  font-size: 14px;
}

.contact .info-item:hover i {
  background: var(--accent-color);
  color: var(--contrast-color);
}

.contact .php-email-form {
  height: 100%;
}

.contact .php-email-form input[type=text],
.contact .php-email-form input[type=email],
.contact .php-email-form textarea {
  font-size: 14px;
  padding: 10px 15px;
  box-shadow: none;
  border-radius: 0;
  color: var(--default-color);
  background-color: color-mix(in srgb, var(--background-color), transparent 50%);
  border-color: color-mix(in srgb, var(--default-color), transparent 80%);
}

.contact .php-email-form input[type=text]:focus,
.contact .php-email-form input[type=email]:focus,
.contact .php-email-form textarea:focus {
  border-color: var(--accent-color);
}

.contact .php-email-form input[type=text]::placeholder,
.contact .php-email-form input[type=email]::placeholder,
.contact .php-email-form textarea::placeholder {
  color: color-mix(in srgb, var(--default-color), transparent 70%);
}

.contact .php-email-form button[type=submit] {
  color: var(--contrast-color);
  background: var(--accent-color);
  border: 0;
  padding: 10px 30px;
  transition: 0.4s;
  border-radius: 50px;
}

.contact .php-email-form button[type=submit]:hover {
  background: color-mix(in srgb, var(--accent-color), transparent 25%);
}

/*--------------------------------------------------------------
# Portfolio Details Section
--------------------------------------------------------------*/
.portfolio-details .portfolio-details-slider img {
  width: 100%;
}

.portfolio-details .swiper-wrapper {
  height: auto;
}

.portfolio-details .swiper-button-prev,
.portfolio-details .swiper-button-next {
  width: 48px;
  height: 48px;
}

.portfolio-details .swiper-button-prev:after,
.portfolio-details .swiper-button-next:after {
  color: rgba(255, 255, 255, 0.8);
  background-color: rgba(0, 0, 0, 0.15);
  font-size: 24px;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.3s;
}

.portfolio-details .swiper-button-prev:hover:after,
.portfolio-details .swiper-button-next:hover:after {
  background-color: rgba(0, 0, 0, 0.3);
}

@media (max-width: 575px) {

  .portfolio-details .swiper-button-prev,
  .portfolio-details .swiper-button-next {
    display: none;
  }
}

.portfolio-details .swiper-pagination {
  margin-top: 20px;
  position: relative;
}

.portfolio-details .swiper-pagination .swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  background-color: color-mix(in srgb, var(--default-color), transparent 85%);
  opacity: 1;
}

.portfolio-details .swiper-pagination .swiper-pagination-bullet-active {
  background-color: var(--accent-color);
}

.portfolio-details .portfolio-info h3 {
  font-size: 22px;
  font-weight: 700;
  margin-bottom: 20px;
  padding-bottom: 20px;
  position: relative;
}

.portfolio-details .portfolio-info h3:after {
  content: "";
  position: absolute;
  display: block;
  width: 50px;
  height: 3px;
  background: var(--accent-color);
  left: 0;
  bottom: 0;
}

.portfolio-details .portfolio-info ul {
  list-style: none;
  padding: 0;
  font-size: 15px;
}

.portfolio-details .portfolio-info ul li {
  display: flex;
  flex-direction: column;
  padding-bottom: 15px;
}

.portfolio-details .portfolio-info ul strong {
  text-transform: uppercase;
  font-weight: 400;
  color: color-mix(in srgb, var(--default-color), transparent 50%);
  font-size: 14px;
}

.portfolio-details .portfolio-info .btn-visit {
  padding: 8px 40px;
  background: var(--accent-color);
  color: var(--contrast-color);
  border-radius: 50px;
  transition: 0.3s;
}

.portfolio-details .portfolio-info .btn-visit:hover {
  background: color-mix(in srgb, var(--accent-color), transparent 20%);
}

.portfolio-details .portfolio-description h2 {
  font-size: 26px;
  font-weight: 700;
  margin-bottom: 20px;
}

.portfolio-details .portfolio-description p {
  padding: 0;
}

.portfolio-details .portfolio-description .testimonial-item {
  padding: 30px 30px 0 30px;
  position: relative;
  background: color-mix(in srgb, var(--default-color), transparent 97%);
  margin-bottom: 50px;
}

.portfolio-details .portfolio-description .testimonial-item .testimonial-img {
  width: 90px;
  border-radius: 50px;
  border: 6px solid var(--background-color);
  float: left;
  margin: 0 10px 0 0;
}

.portfolio-details .portfolio-description .testimonial-item h3 {
  font-size: 18px;
  font-weight: bold;
  margin: 15px 0 5px 0;
  padding-top: 20px;
}

.portfolio-details .portfolio-description .testimonial-item h4 {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
}

.portfolio-details .portfolio-description .testimonial-item .quote-icon-left,
.portfolio-details .portfolio-description .testimonial-item .quote-icon-right {
  color: color-mix(in srgb, var(--accent-color), transparent 50%);
  font-size: 26px;
  line-height: 0;
}

.portfolio-details .portfolio-description .testimonial-item .quote-icon-left {
  display: inline-block;
  left: -5px;
  position: relative;
}

.portfolio-details .portfolio-description .testimonial-item .quote-icon-right {
  display: inline-block;
  right: -5px;
  position: relative;
  top: 10px;
  transform: scale(-1, -1);
}

.portfolio-details .portfolio-description .testimonial-item p {
  font-style: italic;
  margin: 0 0 15px 0 0 0;
  padding: 0;
}

/*--------------------------------------------------------------
# Service Details Section
--------------------------------------------------------------*/
.service-details .service-box {
  padding: 20px;
  box-shadow: 0px 2px 20px rgba(0, 0, 0, 0.1);
}

.service-details .service-box+.service-box {
  margin-top: 30px;
}

.service-details .service-box h4 {
  font-size: 20px;
  font-weight: 700;
  border-bottom: 2px solid color-mix(in srgb, var(--default-color), transparent 92%);
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.service-details .services-list a {
  color: color-mix(in srgb, var(--default-color), transparent 20%);
  background-color: color-mix(in srgb, var(--default-color), transparent 96%);
  display: flex;
  align-items: center;
  padding: 12px 15px;
  margin-top: 15px;
  transition: 0.3s;
}

.service-details .services-list a:first-child {
  margin-top: 0;
}

.service-details .services-list a i {
  font-size: 16px;
  margin-right: 8px;
  color: var(--accent-color);
}

.service-details .services-list a.active {
  color: var(--contrast-color);
  background-color: var(--accent-color);
}

.service-details .services-list a.active i {
  color: var(--contrast-color);
}

.service-details .services-list a:hover {
  background-color: color-mix(in srgb, var(--accent-color), transparent 95%);
  color: var(--accent-color);
}

.service-details .download-catalog a {
  color: var(--default-color);
  display: flex;
  align-items: center;
  padding: 10px 0;
  transition: 0.3s;
  border-top: 1px solid color-mix(in srgb, var(--default-color), transparent 90%);
}

.service-details .download-catalog a:first-child {
  border-top: 0;
  padding-top: 0;
}

.service-details .download-catalog a:last-child {
  padding-bottom: 0;
}

.service-details .download-catalog a i {
  font-size: 24px;
  margin-right: 8px;
  color: var(--accent-color);
}

.service-details .download-catalog a:hover {
  color: var(--accent-color);
}

.service-details .help-box {
  background-color: var(--accent-color);
  color: var(--contrast-color);
  margin-top: 30px;
  padding: 30px 15px;
}

.service-details .help-box .help-icon {
  font-size: 48px;
}

.service-details .help-box h4,
.service-details .help-box a {
  color: var(--contrast-color);
}

.service-details .services-img {
  margin-bottom: 20px;
}

.service-details h3 {
  font-size: 26px;
  font-weight: 700;
}

.service-details p {
  font-size: 15px;
}

.service-details ul {
  list-style: none;
  padding: 0;
  font-size: 15px;
}

.service-details ul li {
  padding: 5px 0;
  display: flex;
  align-items: center;
}

.service-details ul i {
  font-size: 20px;
  margin-right: 8px;
  color: var(--accent-color);
}

/*--------------------------------------------------------------
# Starter Section Section
--------------------------------------------------------------*/
.starter-section {
  padding: 60px 0;
  background: #f8f9fa;
}


/* Hobby Section */
.hobby-subtitle {
    text-align: center;
    color: #666;
    margin-bottom: 2rem;
    margin-top: 35px;
    font-size: 0.95rem;
    line-height: 1.6;
}

.hobby-subtitle::after {
    content: '';
    display: block;
    width: 100px;
    height: 1px;
    background: #ddd;
    margin: 1rem auto 0;
}

.hobby-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
    margin-top: 35px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hobby-image {
    width: 100%;
    aspect-ratio: 1;
    object-fit: cover;
    border-radius: 8px;
    transition: opacity 0.5s ease-in-out;
}

.hobby-link {
    display: block;
    text-align: center;
    color: #222;
    text-decoration: none;
    cursor: pointer;
    transition: color 0.3s;
    font-weight: 500;
    font-style: italic;
}

.hobby-link:hover {
    color: #666;
}


/* Responsive */
@media (max-width: 768px) {
    .header-container {
        flex-direction: row;
        gap: 1rem;
        padding: 0.75rem 1.5rem;
        justify-content: space-between;
    }

    .logo {
        height: 50px;
    }

    nav {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-end;
    }

    .nav-links {
        display: none !important;
    }

    .lang-toggle {
        font-size: 0.85rem;
    }

    section {
        padding: 3rem 0;
    }

    section h2 {
        font-size: 2rem;
    }

    .section-container {
        padding: 0 1.5rem;
    }

    .footer-container {
        grid-template-columns: 1fr;
    }

    .social-icons {
        justify-content: flex-start !important;
    }

    .main-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .main-text h1 {
        font-size: 2.5rem;
    }

    .profile-image {
        width: 200px;
        height: 200px;
    }

    .button-group {
        justify-content: center;
    }

    .circle-btn {
        width: 100px;
        height: 100px;
        font-size: 0.9rem;
    }

    .resume-tabs {
        flex-wrap: wrap;
        gap: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .tab-btn {
        font-size: 0.9rem;
        padding: 0.3rem 0;
    }

    .tab-separator {
        display: none;
    }

    .timeline::before {
        display: none;
    }

    .timeline-item:nth-child(odd) .timeline-content,
    .timeline-item:nth-child(even) .timeline-content {
        width: 100%;
        margin-left: 0;
        text-align: left;
    }

    .timeline-dot {
        display: none;
    }

    .timeline-content {
        padding: 1.25rem;
        gap: 1rem;
        flex-direction: column !important;
        align-items: flex-start !important;
    }

    .timeline-logo {
        width: 80px;
        height: 80px;
        min-width: 80px;
    }

    .timeline-content h3 {
        font-size: 0.95rem;
    }

    .education-degree {
        font-size: 0.85rem;
    }

    .timeline-content .year {
        font-size: 0.8rem;
    }

    .timeline-content ul {
        font-size: 0.85rem;
    }

    .skills-list {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .skill-item {
        padding: 0.85rem;
        font-size: 0.9rem;
    }

    .work-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .work-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .work-logo {
        width: 80px;
        height: 80px;
        min-width: 80px;
    }

    .work-card h3 {
        font-size: 1.1rem;
    }

    .work-card .company {
        font-size: 0.9rem;
    }

    .work-card .period {
        font-size: 0.8rem;
    }

    .work-card p {
        font-size: 0.9rem;
    }

    /* Certifications */
    .cert-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .cert-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .cert-logo {
        width: 80px;
        height: 80px;
        min-width: 80px;
    }

    .cert-info h3 {
        font-size: 1.1rem;
    }

    .cert-info .issuer {
        font-size: 0.9rem;
    }

    /* Modals */
    .pdf-modal-content {
        width: 95%;
        height: 90vh;
    }

    /* Projects */
    .project-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    /* Footer improvements */
    .footer-column {
        margin-bottom: 1.5rem;
    }

    .footer-column h3 {
        font-size: 1rem;
    }

    .footer-column p {
        font-size: 0.9rem;
    }

    /* Stats grid for mobile */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        font-size: 2rem;
    }

    .stat-label {
        font-size: 0.9rem;
        align-items: flex-start;
        text-align: left;
        padding: 1.25rem;
        gap: 1rem;
    }

    .work-logo {
        width: 80px;
        height: 80px;
        min-width: 80px;
    }

    .work-card h3 {
        font-size: 1rem;
    }

    .work-card .position {
        font-size: 0.85rem;
    }

    .work-card .year {
        font-size: 0.8rem;
    }

    .work-card ul {
        font-size: 0.85rem;
    }

    .certification-item {
        flex-direction: row;
        align-items: flex-start;
        text-align: left;
        padding: 1rem;
        gap: 1rem;
    }

    .cert-logo {
        width: 80px;
        height: 80px;
        min-width: 80px;
    }

    .cert-info h3 {
        font-size: 0.95rem;
    }

    .cert-info p {
        font-size: 0.8rem;
    }

    .pdf-modal-content {
        width: 95%;
        height: 90vh;
    }

    .project-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

/* Extra small devices */
@media (max-width: 480px) {
    .header-container {
        padding: 0.6rem 1rem;
        flex-direction: row;
        justify-content: space-between;
    }

    .logo {
        height: 40px;
    }

    nav {
        align-items: flex-end;
    }

    .nav-links {
        display: none;
    }

    .lang-toggle {
        font-size: 0.8rem;
    }

    .section-container {
        padding: 0 1rem;
    }

    .footer-container {
        padding: 0 1rem;
    }

    .footer-bottom {
        padding: 1.5rem 1rem 0;
    }

    .footer-bottom p {
        white-space: normal;
    }

    .social-icons {
        justify-content: flex-start !important;
    }

    .social-link {
        width: 45px;
        height: 45px;
        font-size: 1.4rem !important;
    }

    .main-text h1 {
        font-size: 2rem;
    }

    .main-text p {
        font-size: 1rem;
    }

    .profile-image {
        width: 150px;
        height: 150px;
    }

    .circle-btn {
        width: 80px;
        height: 80px;
        font-size: 0.8rem;
    }

    .resume-tabs {
        gap: 0.3rem 0.75rem;
        font-size: 0.8rem;
    }

    .tab-btn {
        font-size: 0.8rem;
    }

    .tab-btn::after {
        font-size: 0.7rem;
        padding: 0.35rem 0.5rem;
        margin-bottom: 0.3rem;
    }

    .timeline::before {
        display: none;
    }

    .timeline-item:nth-child(odd) .timeline-content,
    .timeline-item:nth-child(even) .timeline-content {
        margin-left: 0;
        width: 100%;
    }

    .timeline-dot {
        display: none;
    }

    .timeline-content {
        padding: 1rem;
        gap: 1rem;
    }

    .timeline-logo {
        width: 70px;
        height: 70px;
        min-width: 70px;
    }

    .timeline-content h3 {
        font-size: 0.85rem;
    }

    .education-degree {
        font-size: 0.8rem;
    }

    .timeline-content .year {
        font-size: 0.75rem;
    }

    .timeline-content ul {
        font-size: 0.8rem;
    }

    .work-card {
        padding: 1rem;
        gap: 0.75rem;
    }

    .work-logo {
        width: 60px;
        height: 60px;
        min-width: 60px;
    }

    .work-card h3 {
        font-size: 0.9rem;
    }

    .work-card .position {
        font-size: 0.8rem;
    }

    .work-card .year {
        font-size: 0.75rem;
    }

    .work-card ul {
        font-size: 0.8rem;
    }

    .certification-item {
        padding: 0.75rem;
        gap: 0.75rem;
    }

    .cert-logo {
        width: 60px;
        height: 60px;
        min-width: 60px;
    }

    .cert-info h3 {
        font-size: 0.85rem;
    }

    .cert-info p {
        font-size: 0.75rem;
    }

    .cert-pdf-btn {
        font-size: 0.75rem;
        padding: 0.4rem 0.8rem;
    }

    .skills-list {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .skill-item {
        padding: 0.7rem;
        font-size: 0.85rem;
    }

    .project-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .project-logo {
        width: 100px;
        height: 100px;
    }

    .hobby-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
    }

    .modal-content {
        width: 95%;
        padding: 1.5rem;
    }

    .modal-content h3 {
        font-size: 1.3rem;
    }
}

/* Medium devices (tablets) */
@media (min-width: 481px) and (max-width: 768px) {
    .project-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .hobby-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .skills-list {
        grid-template-columns: repeat(3, 1fr);
    }

    .main {
        margin-left: 0 !important;
    }

    .hero {
        padding: 50px 0;
        min-height: 75vh;
    }

    .hero h2 {
        font-size: 2.2rem;
    }
}

/* Large devices (desktops) - Additional fixes */
@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }

    .section {
        padding: 80px 0;
    }

    /* Ensure proper spacing for desktop */
    .hero {
        padding: 100px 0;
    }
}

/* Extra large devices */
@media (min-width: 1400px) {
    .container {
        max-width: 1320px;
    }
}

/* Viewport fixes for all devices */
@media screen and (max-width: 1199px) {
    .main {
        margin-left: 0 !important;
        width: 100%;
    }
}

/* Force center layout for all screen sizes */
.main {
    margin-left: 0 !important;
    width: 100%;
    max-width: 100%;
}

/* Ensure all sections are centered */
.section {
    width: 100%;
    margin: 0 auto;
}

.container {
    margin: 0 auto;
    width: 100%;
}

/* Ensure no horizontal scroll */
* {
    max-width: 100%;
}

html, body {
    overflow-x: hidden;
    width: 100%;
}

/* Override any desktop margin settings - Keep content centered */
@media (min-width: 1200px) {
    .main {
        margin-left: 0 !important;
        width: 100% !important;
        padding-left: 0 !important;
    }

    .header~main {
        margin-left: 0 !important;
        width: 100% !important;
    }

    .header~main .hero {
        margin-left: 0 !important;
        width: 100vw !important;
    }

    /* Ensure sidebar doesn't affect content positioning */
    .section {
        width: 100%;
        margin: 0 auto;
    }

    .container {
        margin: 0 auto !important;
        max-width: 1200px;
    }

    /* Make sidebar overlay instead of pushing content */
    .header {
        position: fixed;
        z-index: 997;
    }
}
